import cv2
import os

print("Checking all images in the images folder:")
for filename in sorted(os.listdir('images')):
    if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
        img_path = f'images/{filename}'
        img = cv2.imread(img_path)
        if img is not None:
            print(f"✅ {filename}: {img.shape}")
        else:
            print(f"❌ {filename}: Failed to load")
