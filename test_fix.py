#!/usr/bin/env python3

import cv2
import numpy as np
import sys
import time

def test_imports():
    """Test all imports"""
    print("Testing imports...")

    try:
        import utlis
        print("✅ utlis imported successfully")
    except Exception as e:
        print(f"❌ Failed to import utlis: {e}")
        return False

    try:
        import torch
        print(f"✅ PyTorch {torch.__version__} imported successfully")
    except Exception as e:
        print(f"❌ Failed to import torch: {e}")
        return False

    try:
        from pytorch_unet.unet_predict import UnetModel, Res34BackBone
        print("✅ UNet model imported successfully")
    except Exception as e:
        print(f"❌ Failed to import UNet model: {e}")
        return False

    try:
        from find_nearest_box import NearestBox
        print("✅ NearestBox imported successfully")
    except Exception as e:
        print(f"❌ Failed to import NearestBox: {e}")
        return False

    try:
        import detect_face
        print("✅ detect_face imported successfully")
    except Exception as e:
        print(f"❌ Failed to import detect_face: {e}")
        return False

    try:
        from extract_words import OcrFactory
        print("✅ OcrFactory imported successfully")
    except Exception as e:
        print(f"❌ Failed to import OcrFactory: {e}")
        return False

    return True

def test_text_detection():
    """Test the OpenCV-based text detection"""
    print("\nTesting text detection...")

    # Load test image
    img_path = "images/ori14_m2rot.jpg"
    img = cv2.imread(img_path)

    if img is None:
        print(f"❌ Failed to load image: {img_path}")
        return False

    print(f"✅ Image loaded successfully: {img.shape}")

    try:
        import utlis
        # Test the text detection function
        start_time = time.time()
        heatmap, boxes = utlis.createHeatMapAndBoxCoordinates(img)
        end_time = time.time()

        print(f"✅ Text detection successful! Found {len(boxes)} text regions in {end_time - start_time:.2f}s")
        print(f"✅ Heatmap shape: {heatmap.shape}")

        # Test perspective correction
        start_time = time.time()
        corrected_img = utlis.correctPerspective(img)
        end_time = time.time()

        print(f"✅ Perspective correction successful: {corrected_img.shape} in {end_time - start_time:.2f}s")

        return True

    except Exception as e:
        print(f"❌ Error during text detection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_models():
    """Test model initialization"""
    print("\nTesting model initialization...")

    try:
        import torch
        from pytorch_unet.unet_predict import UnetModel, Res34BackBone

        use_cuda = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {use_cuda}")

        start_time = time.time()
        model = UnetModel(Res34BackBone(), use_cuda)
        end_time = time.time()

        print(f"✅ UNet model initialized successfully in {end_time - start_time:.2f}s")
        return True

    except Exception as e:
        print(f"❌ Error initializing UNet model: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_face_detection():
    """Test face detection"""
    print("\nTesting face detection...")

    try:
        import detect_face

        start_time = time.time()
        face_detector = detect_face.face_factory(face_model="ssd")
        findFaceID = face_detector.get_face_detector()
        end_time = time.time()

        print(f"✅ Face detector initialized successfully in {end_time - start_time:.2f}s")
        return True

    except Exception as e:
        print(f"❌ Error initializing face detector: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr():
    """Test OCR initialization"""
    print("\nTesting OCR initialization...")

    try:
        from extract_words import OcrFactory

        start_time = time.time()
        Image2Text = OcrFactory().select_ocr_method(ocr_method="EasyOcr", border_thresh=3, denoise=False)
        end_time = time.time()

        print(f"✅ OCR initialized successfully in {end_time - start_time:.2f}s")
        return True

    except Exception as e:
        print(f"❌ Error initializing OCR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Testing ID-OCR components after CRAFT fix...\n")

    all_passed = True

    # Test imports
    if not test_imports():
        all_passed = False

    # Test text detection
    if not test_text_detection():
        all_passed = False

    # Test models (this might take time)
    print("\n⏳ Testing model initialization (this may take a while)...")
    if not test_models():
        all_passed = False

    # Test face detection
    if not test_face_detection():
        all_passed = False

    # Test OCR
    if not test_ocr():
        all_passed = False

    print("\n" + "="*50)
    if all_passed:
        print("✅ All tests passed! The CRAFT compatibility fix is working correctly.")
        print("🚀 You can now run the main script with: python main.py --folder_name images")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)
