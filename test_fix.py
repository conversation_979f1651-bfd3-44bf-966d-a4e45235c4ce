#!/usr/bin/env python3

import cv2
import numpy as np
import utlis

def test_text_detection():
    """Test the EasyOCR-based text detection"""
    print("Testing text detection...")
    
    # Load test image
    img_path = "images/ori14_m2rot.jpg"
    img = cv2.imread(img_path)
    
    if img is None:
        print(f"Failed to load image: {img_path}")
        return False
    
    print(f"Image loaded successfully: {img.shape}")
    
    try:
        # Test the text detection function
        heatmap, boxes = utlis.createHeatMapAndBoxCoordinates(img)
        print(f"Text detection successful! Found {len(boxes)} text regions")
        print(f"Heatmap shape: {heatmap.shape}")
        
        # Test some other utility functions
        print("Testing other utility functions...")
        
        # Test perspective correction
        corrected_img = utlis.correctPerspective(img)
        print(f"Perspective correction successful: {corrected_img.shape}")
        
        return True
        
    except Exception as e:
        print(f"Error during text detection: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_text_detection()
    if success:
        print("\n✅ All tests passed! The fix is working correctly.")
        print("You can now run the main script with: python main.py --folder_name images")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
